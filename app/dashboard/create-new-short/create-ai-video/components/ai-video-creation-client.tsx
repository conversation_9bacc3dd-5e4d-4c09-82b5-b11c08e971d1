'use client';

import React, { useState, useContext } from 'react';
import { UserDetailContext } from "@/context/UserDetailContext";
import { toast } from 'sonner';

// Import shared hooks and components (MASSIVE code reduction)
import { useSharedAuth } from '@/hooks/useSharedAuth';
import { useSharedVideoGeneration } from '@/hooks/useSharedVideoGeneration';
import { useSharedFormValidation, commonValidationRules } from '@/hooks/useSharedFormValidation';
import { VideoPageLayout, VideoTwoColumnLayout, FormSection, PreviewSection } from '@/components/shared/VideoPageLayout';
import { AIVideoGenerationButton } from '@/components/shared/GenerationButton';

// Import existing hooks (keep the specialized ones)
import { useAIVideoForm } from '../hooks/useAIVideoForm';
import { aiVideoScriptGeneration } from '@/app/actions/aiVideoActions';
import { triggerAIVideoGeneration } from '@/actions/aiVideoGeneration';
import { Film } from 'lucide-react';

/**
 * AI Video Creation Client Component
 * REFACTORED: Now uses shared hooks and components for massive code reduction
 * Preserves ALL existing functionality while eliminating duplication
 */
export default function AIVideoCreationClient({ initialCredits }) {
  const { userDetail } = useContext(UserDetailContext);

  // Shared authentication (replaces 20+ lines of auth logic)
  const auth = useSharedAuth();

  // Form state management (existing hook - keep specialized functionality)
  const {
    formData,
    updateField,
    updateFields,
    getSubmissionData
  } = useAIVideoForm();

  // Shared video generation (replaces 100+ lines of generation logic)
  const {
    isGenerating,
    generateVideo,
    hasSufficientCredits,
    requiredCredits
  } = useSharedVideoGeneration('ai-video', 10);

  // Shared form validation (replaces 50+ lines of validation logic)
  const validationRules = {
    ...commonValidationRules,
    topic: commonValidationRules.topic,
    script: commonValidationRules.script,
    videoStyle: { required: true, message: "Please select a video style" },
    voice: { required: true, message: "Please select a voice" },
    aspectRatio: { required: true, message: "Please select an aspect ratio" }
  };

  const { validateFormWithToast } = useSharedFormValidation(validationRules);

  // Local state for script generation (keep specialized functionality)
  const [isGeneratingScript, setIsGeneratingScript] = useState(false);

  // Early returns for auth states (using shared auth)
  if (auth.isLoading) return <auth.LoadingComponent />;
  if (auth.isUnauthorized) return <auth.UnauthorizedComponent />;

  // Script generation handler (keep specialized functionality)
  const handleGenerateScript = async () => {
    if (!formData.topic?.trim()) {
      toast.error('Please enter a topic first');
      return;
    }

    if (!formData.videoStyle) {
      toast.error('Please select a video style first');
      return;
    }

    setIsGeneratingScript(true);

    try {
      const result = await aiVideoScriptGeneration({
        topic: formData.topic,
        videoStyle: formData.videoStyle
      });

      if (result.success && result.script) {
        updateField('script', result.script);
        toast.success('Script generated successfully!');
      } else {
        throw new Error(result.error || 'Failed to generate script');
      }
    } catch (error) {
      console.error('Script generation error:', error);
      toast.error('Failed to generate script. Please try again.');
    } finally {
      setIsGeneratingScript(false);
    }
  };

  // Video generation handler (using shared logic)
  const handleGenerateVideo = async () => {
    // Validate form using shared validation
    if (!validateFormWithToast(formData)) {
      return false;
    }

    // Get clean submission data and generate using shared hook
    const submissionData = getSubmissionData();
    return await generateVideo(submissionData, triggerAIVideoGeneration);
  };

  // Get current credits
  const currentCredits = userDetail?.credits || initialCredits || 0;

  return (
    <VideoPageLayout
      title="AI Video Creator"
      description="Create engaging videos with AI-generated scripts, professional voiceovers, and dynamic visuals. Perfect for social media, marketing, and educational content."
      emoji="✨"
      credits={currentCredits}
      gradientFrom="blue"
    >
      <VideoTwoColumnLayout
        leftColumn={
          <>
            {/* Project Setup Section */}
            <FormSection
              title="Project Setup"
              description="Configure your AI video project"
              icon="📝"
            >
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Project Title</label>
                  <input
                    type="text"
                    value={formData.projectTitle}
                    onChange={(e) => updateField('projectTitle', e.target.value)}
                    placeholder="e.g., My AI Video"
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </FormSection>

            {/* Script Generation Section */}
            <FormSection
              title="Video Script"
              description="Create your video script using AI or write it manually"
              icon="📝"
            >
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Topic</label>
                  <input
                    type="text"
                    value={formData.topic}
                    onChange={(e) => updateField('topic', e.target.value)}
                    placeholder="Enter your video topic..."
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Video Style</label>
                  <select
                    value={formData.videoStyle}
                    onChange={(e) => updateField('videoStyle', e.target.value)}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select style...</option>
                    <option value="educational">Educational</option>
                    <option value="entertaining">Entertaining</option>
                    <option value="promotional">Promotional</option>
                    <option value="informational">Informational</option>
                  </select>
                </div>

                <button
                  onClick={handleGenerateScript}
                  disabled={isGeneratingScript || !formData.topic || !formData.videoStyle}
                  className="w-full bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {isGeneratingScript ? 'Generating Script...' : 'Generate AI Script'}
                </button>

                <div>
                  <label className="block text-sm font-medium mb-2">Script</label>
                  <textarea
                    value={formData.script}
                    onChange={(e) => updateField('script', e.target.value)}
                    placeholder="Your script will appear here..."
                    rows={6}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </FormSection>

            {/* Video Configuration Section */}
            <FormSection
              title="Video Configuration"
              description="Configure video settings"
              icon="⚙️"
            >
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Aspect Ratio</label>
                  <select
                    value={formData.aspectRatio}
                    onChange={(e) => updateField('aspectRatio', e.target.value)}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select ratio...</option>
                    <option value="9:16">9:16 (Vertical)</option>
                    <option value="16:9">16:9 (Horizontal)</option>
                    <option value="1:1">1:1 (Square)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Voice</label>
                  <select
                    value={formData.voice}
                    onChange={(e) => updateField('voice', e.target.value)}
                    className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select voice...</option>
                    <option value="male">Male Voice</option>
                    <option value="female">Female Voice</option>
                  </select>
                </div>
              </div>
            </FormSection>
          </>
        }
        rightColumn={
          <PreviewSection>
            <div className="space-y-4">
              {/* Simple Preview */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <div className="text-gray-500">
                  <Film className="mx-auto h-12 w-12 mb-4" />
                  <p>Video preview will appear here</p>
                </div>
              </div>

              {/* Generation Button */}
              <AIVideoGenerationButton
                onClick={handleGenerateVideo}
                isGenerating={isGenerating}
                disabled={!formData.projectTitle || !formData.script || !formData.aspectRatio || !formData.voice}
                credits={currentCredits}
                requiredCredits={requiredCredits}
              />
            </div>
          </PreviewSection>
        }
      />
    </VideoPageLayout>
  );
}
